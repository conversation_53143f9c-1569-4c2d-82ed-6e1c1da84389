import 'package:flutter/foundation.dart';
import 'network_client.dart';

/// Web版本配置管理
class WebConfig {
  static bool _initialized = false;
  
  /// 初始化Web版本配置
  static void initialize({
    String? proxyUrl,
  }) {
    if (!kIsWeb || _initialized) {
      return;
    }
    
    print('🌐 初始化Web版本配置...');
    
    // 设置代理地址
    if (proxyUrl != null) {
      WebProxyClient.setProxyUrl(proxyUrl);
      print('✅ 使用自定义代理地址: $proxyUrl');
    } else {
      // 自动检测代理地址
      _autoConfigureProxy();
    }
    
    _initialized = true;
    print('✅ Web版本配置初始化完成');
  }
  
  /// 自动配置代理
  static void _autoConfigureProxy() {
    // 根据当前域名自动配置代理
    final currentHost = Uri.base.host;
    String proxyUrl;
    
    if (currentHost.contains('dznovel.top')) {
      // 生产环境 - 通过Nginx代理，不需要指定端口
      proxyUrl = 'https://www.dznovel.top';
    } else if (currentHost == 'localhost' || currentHost == '127.0.0.1') {
      // 本地开发环境
      proxyUrl = 'http://localhost:8081';
    } else {
      // 其他环境，使用默认配置
      proxyUrl = 'http://localhost:8081';
    }
    
    WebProxyClient.setProxyUrl(proxyUrl);
    print('🔧 自动配置代理地址: $proxyUrl');
  }
  
  /// 获取推荐的代理配置
  static Map<String, String> getRecommendedProxyConfig() {
    final currentHost = Uri.base.host;
    
    return {
      'development': 'http://localhost:8081',
      'production': 'https://www.dznovel.top',
      'current': currentHost.contains('dznovel.top')
          ? 'https://www.dznovel.top'
          : 'http://localhost:8081',
    };
  }
  
  /// 测试代理连接
  static Future<bool> testProxyConnection() async {
    if (!kIsWeb) {
      return true;
    }
    
    try {
      final client = WebProxyClient();
      final testUrl = 'https://httpbin.org/get';
      
      print('🧪 测试代理连接: $testUrl');
      
      final response = await client.get(Uri.parse(testUrl)).timeout(
        const Duration(seconds: 10),
      );
      
      final success = response.statusCode == 200;
      print(success ? '✅ 代理连接测试成功' : '❌ 代理连接测试失败: ${response.statusCode}');
      
      client.close();
      return success;
      
    } catch (e) {
      print('❌ 代理连接测试失败: $e');
      return false;
    }
  }
  
  /// 获取Web版本的特殊配置说明
  static String getWebConfigInstructions() {
    return '''
🌐 Web版本配置说明

由于浏览器的安全限制，Web版本需要使用CORS代理服务器来解决跨域和SSL证书验证问题。

📋 部署步骤：

1. 在服务器上部署代理服务器：
   bash deploy_to_bt.sh

2. 配置Nginx反向代理（可选）：
   在宝塔面板中添加location /proxy/配置

3. 测试代理连接：
   curl 'http://your-domain.com:8080/proxy/https://httpbin.org/get'

🔧 当前配置：
代理地址: ${WebProxyClient.getProxyUrl() ?? '未设置'}
当前域名: ${Uri.base.host}

💡 如果遇到连接问题：
1. 确保代理服务器正在运行
2. 检查防火墙设置
3. 验证域名和端口配置
''';
  }
}
