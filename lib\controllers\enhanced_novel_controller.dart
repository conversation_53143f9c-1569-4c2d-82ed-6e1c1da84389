import 'package:get/get.dart';
import '../models/world_building.dart' as world_building;
import '../models/character_profile.dart';
import '../models/character_card.dart';
import '../models/novel.dart';
import '../langchain/services/enhanced_novel_generation_service.dart';
import '../langchain/models/novel_memory.dart';
import '../langchain/models/writing_style_package.dart';

/// 增强小说功能控制器
class EnhancedNovelController extends GetxService {
  final EnhancedNovelGenerationService _enhancedService = Get.find<EnhancedNovelGenerationService>();

  // 当前状态
  final isGenerating = false.obs;
  final currentStep = ''.obs;
  final progress = 0.0.obs;

  // 数据存储
  final Rx<world_building.WorldBuilding?> currentWorldBuilding = Rx<world_building.WorldBuilding?>(null);
  final RxMap<String, CharacterProfile> currentCharacterProfiles = <String, CharacterProfile>{}.obs;

  /// 生成完整的增强小说
  Future<void> generateEnhancedNovel({
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required int totalChapters,
    required Map<String, CharacterCard> characterCards,
    String? background,
    String? otherRequirements,
    WritingStylePackage? writingStyle,
    String? sessionId,
  }) async {
    try {
      isGenerating.value = true;
      progress.value = 0.0;

      // 第一步：生成世界观
      currentStep.value = '正在生成世界观...';
      final worldBuilding = await _enhancedService.generateWorldBuilding(
        novelTitle: novelTitle,
        genres: genres,
        theme: theme,
        targetReaders: targetReaders,
        totalChapters: totalChapters,
        background: background,
        otherRequirements: otherRequirements,
        characterCards: characterCards,
        onProgress: (message) => currentStep.value = message,
        sessionId: sessionId,
      );
      currentWorldBuilding.value = worldBuilding;
      progress.value = 0.2;

      // 第二步：生成角色档案
      currentStep.value = '正在生成角色档案...';
      final characterProfiles = await _enhancedService.generateCharacterProfiles(
        worldBuilding: worldBuilding,
        characterCards: characterCards,
        onProgress: (message) => currentStep.value = message,
        sessionId: sessionId,
      );
      currentCharacterProfiles.value = characterProfiles;
      progress.value = 0.4;

      // 第三步：生成增强大纲
      currentStep.value = '正在生成增强大纲...';
      final outline = await _enhancedService.generateEnhancedOutline(
        novelTitle: novelTitle,
        genres: genres,
        theme: theme,
        targetReaders: targetReaders,
        totalChapters: totalChapters,
        worldBuilding: worldBuilding,
        characterProfiles: characterProfiles,
        background: background,
        otherRequirements: otherRequirements,
        writingStyle: writingStyle,
        onProgress: (message) => currentStep.value = message,
        sessionId: sessionId,
      );
      progress.value = 0.6;

      // 第四步：逐章生成内容
      for (int i = 1; i <= totalChapters; i++) {
        currentStep.value = '正在生成第$i章内容...';
        
        // 这里需要从大纲中提取章节信息
        final chapterTitle = _extractChapterTitle(outline, i);
        final chapterOutline = _extractChapterOutline(outline, i);
        
        await _enhancedService.generateChapterWithProfileUpdate(
          novelTitle: novelTitle,
          chapterNumber: i,
          chapterTitle: chapterTitle,
          outlineContent: chapterOutline,
          genres: genres,
          theme: theme,
          targetReaders: targetReaders,
          worldBuilding: worldBuilding,
          characterProfiles: currentCharacterProfiles,
          background: background,
          otherRequirements: otherRequirements,
          writingStyle: writingStyle,
          onProgress: (message) => currentStep.value = message,
          sessionId: sessionId,
        );

        progress.value = 0.6 + (0.4 * i / totalChapters);
      }

      currentStep.value = '增强小说生成完成！';
      progress.value = 1.0;

    } catch (e) {
      currentStep.value = '生成失败: $e';
      print('生成增强小说时出错: $e');
      rethrow;
    } finally {
      isGenerating.value = false;
    }
  }

  /// 单独生成世界观
  Future<world_building.WorldBuilding> generateWorldBuilding({
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required int totalChapters,
    required Map<String, CharacterCard> characterCards,
    String? background,
    String? otherRequirements,
    String? sessionId,
  }) async {
    try {
      isGenerating.value = true;
      currentStep.value = '正在生成世界观...';

      final worldBuilding = await _enhancedService.generateWorldBuilding(
        novelTitle: novelTitle,
        genres: genres,
        theme: theme,
        targetReaders: targetReaders,
        totalChapters: totalChapters,
        background: background,
        otherRequirements: otherRequirements,
        characterCards: characterCards,
        onProgress: (message) => currentStep.value = message,
        sessionId: sessionId,
      );

      currentWorldBuilding.value = worldBuilding;
      currentStep.value = '世界观生成完成！';
      
      return worldBuilding;
    } catch (e) {
      currentStep.value = '世界观生成失败: $e';
      rethrow;
    } finally {
      isGenerating.value = false;
    }
  }

  /// 单独生成角色档案
  Future<Map<String, CharacterProfile>> generateCharacterProfiles({
    required WorldBuilding worldBuilding,
    required Map<String, CharacterCard> characterCards,
    String? sessionId,
  }) async {
    try {
      isGenerating.value = true;
      currentStep.value = '正在生成角色档案...';

      final profiles = await _enhancedService.generateCharacterProfiles(
        worldBuilding: worldBuilding,
        characterCards: characterCards,
        onProgress: (message) => currentStep.value = message,
        sessionId: sessionId,
      );

      currentCharacterProfiles.value = profiles;
      currentStep.value = '角色档案生成完成！';
      
      return profiles;
    } catch (e) {
      currentStep.value = '角色档案生成失败: $e';
      rethrow;
    } finally {
      isGenerating.value = false;
    }
  }

  /// 生成单个章节并更新角色档案
  Future<String> generateChapterWithUpdate({
    required String novelTitle,
    required int chapterNumber,
    required String chapterTitle,
    required String outlineContent,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    String? background,
    String? otherRequirements,
    WritingStylePackage? writingStyle,
    String? sessionId,
  }) async {
    if (currentWorldBuilding.value == null || currentCharacterProfiles.isEmpty) {
      throw Exception('请先生成世界观和角色档案');
    }

    try {
      isGenerating.value = true;
      currentStep.value = '正在生成第$chapterNumber章...';

      final chapterContent = await _enhancedService.generateChapterWithProfileUpdate(
        novelTitle: novelTitle,
        chapterNumber: chapterNumber,
        chapterTitle: chapterTitle,
        outlineContent: outlineContent,
        genres: genres,
        theme: theme,
        targetReaders: targetReaders,
        worldBuilding: currentWorldBuilding.value!,
        characterProfiles: currentCharacterProfiles,
        background: background,
        otherRequirements: otherRequirements,
        writingStyle: writingStyle,
        onProgress: (message) => currentStep.value = message,
        sessionId: sessionId,
      );

      currentStep.value = '第$chapterNumber章生成完成！';
      return chapterContent;
    } catch (e) {
      currentStep.value = '第$chapterNumber章生成失败: $e';
      rethrow;
    } finally {
      isGenerating.value = false;
    }
  }

  /// 加载已有的世界观和角色档案
  Future<void> loadExistingData(String novelTitle, {String? sessionId}) async {
    try {
      final novelMemory = NovelMemory(novelTitle: novelTitle, sessionId: sessionId);
      
      // 加载世界观
      final worldBuildingText = await novelMemory.getEnhancedWorldBuilding();
      if (worldBuildingText != null) {
        // 这里需要实现从文本解析WorldBuilding对象的逻辑
        // 暂时创建一个简单的对象
        currentWorldBuilding.value = WorldBuilding(
          novelId: novelTitle,
          timeBackground: '已加载的世界观',
          geographicalSetting: '已加载的地理设定',
          socialStructure: '已加载的社会结构',
          culturalBackground: '已加载的文化背景',
          complexity: world_building.WorldBuildingComplexity.moderate,
        );
      }

      // 加载角色档案
      final profilesData = await novelMemory.getCharacterProfiles();
      if (profilesData != null) {
        final profiles = <String, CharacterProfile>{};
        for (final entry in profilesData.entries) {
          try {
            profiles[entry.key] = CharacterProfile.fromJson(entry.value);
          } catch (e) {
            print('解析角色档案失败: $e');
          }
        }
        currentCharacterProfiles.value = profiles;
      }
    } catch (e) {
      print('加载已有数据失败: $e');
    }
  }

  /// 获取角色发展历程
  String getCharacterDevelopmentHistory(String characterId) {
    final profile = currentCharacterProfiles[characterId];
    if (profile == null) return '暂无发展记录';
    
    if (profile.developments.isEmpty) return '暂无发展记录';
    
    final buffer = StringBuffer();
    buffer.writeln('${profile.baseInfo.name} 发展历程：\n');
    
    for (final development in profile.developments) {
      buffer.writeln('第${development.chapterNumber}章：${development.development}');
    }
    
    return buffer.toString();
  }

  /// 获取世界观摘要
  String getWorldBuildingSummary() {
    final worldBuilding = currentWorldBuilding.value;
    if (worldBuilding == null) return '暂未生成世界观';
    
    return '''
世界观摘要：
• 时代背景：${worldBuilding.timeBackground}
• 地理环境：${worldBuilding.geographicalSetting}
• 社会结构：${worldBuilding.socialStructure}
• 文化背景：${worldBuilding.culturalBackground}
''';
  }

  /// 重置状态
  void reset() {
    isGenerating.value = false;
    currentStep.value = '';
    progress.value = 0.0;
    currentWorldBuilding.value = null;
    currentCharacterProfiles.clear();
  }

  // ========== 私有辅助方法 ==========

  String _extractChapterTitle(String outline, int chapterNumber) {
    // 简单的章节标题提取逻辑
    final pattern = RegExp('第$chapterNumber章[：:]([^\\n]+)');
    final match = pattern.firstMatch(outline);
    return match?.group(1)?.trim() ?? '第$chapterNumber章';
  }

  String _extractChapterOutline(String outline, int chapterNumber) {
    // 简单的章节大纲提取逻辑
    final lines = outline.split('\n');
    final buffer = StringBuffer();
    bool inChapter = false;
    
    for (final line in lines) {
      if (line.contains('第$chapterNumber章')) {
        inChapter = true;
        buffer.writeln(line);
      } else if (inChapter && line.contains('第${chapterNumber + 1}章')) {
        break;
      } else if (inChapter) {
        buffer.writeln(line);
      }
    }
    
    return buffer.toString().trim();
  }
}
