import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import 'package:flutter/foundation.dart';

/// 网络客户端工具类，确保正确继承系统网络配置
class NetworkClient {
  /// 创建一个能够正确继承系统网络配置的HTTP客户端
  static http.Client createSystemClient({
    Duration? timeout,
    bool enableSystemProxy = true,
  }) {
    if (kIsWeb) {
      // Web平台使用CORS代理客户端
      return WebProxyClient();
    }

    // 创建HttpClient实例
    final httpClient = HttpClient();

    if (enableSystemProxy) {
      // 启用自动重定向
      httpClient.autoUncompress = true;

      // 设置用户代理
      httpClient.userAgent = 'DaiZongAI/1.0 (Windows)';

      // 尝试设置系统代理检测
      try {
        // 检查环境变量中的代理设置
        final httpProxy = Platform.environment['HTTP_PROXY'] ??
            Platform.environment['http_proxy'];
        final httpsProxy = Platform.environment['HTTPS_PROXY'] ??
            Platform.environment['https_proxy'];

        if (httpProxy != null || httpsProxy != null) {
          // 如果环境变量中有代理设置，使用它
          final proxyUrl = httpsProxy ?? httpProxy;
          httpClient.findProxy = (uri) => 'PROXY $proxyUrl';
          print('使用环境变量代理: $proxyUrl');
        } else {
          // 使用系统默认代理检测，让系统自动处理代理
          // 不强制指定代理端口，让HttpClient使用系统配置
          print('使用系统默认代理配置');
        }
      } catch (e) {
        print('设置代理检测失败: $e');
        // 如果设置失败，使用直连
      }
    }

    // 设置超时
    if (timeout != null) {
      httpClient.connectionTimeout = timeout;
      httpClient.idleTimeout = timeout;
    }

    // 完全跳过SSL证书验证（解决所有SSL握手问题）
    httpClient.badCertificateCallback = (cert, host, port) {
      print('跳过SSL证书验证 - 主机: $host, 端口: $port');
      print('证书主题: ${cert.subject}');
      print('证书颁发者: ${cert.issuer}');
      // 对所有主机都返回true，完全跳过证书验证
      return true;
    };

    return IOClient(httpClient);
  }

  /// 创建一个使用指定代理的HTTP客户端
  static http.Client createProxyClient({
    required String proxyHost,
    required int proxyPort,
    Duration? timeout,
  }) {
    if (kIsWeb) {
      throw UnsupportedError('代理客户端在Web平台不受支持');
    }

    final httpClient = HttpClient();

    // 设置代理
    httpClient.findProxy = (uri) => 'PROXY $proxyHost:$proxyPort';

    // 设置超时
    if (timeout != null) {
      httpClient.connectionTimeout = timeout;
      httpClient.idleTimeout = timeout;
    }

    // 启用自动重定向
    httpClient.autoUncompress = true;

    // 设置用户代理
    httpClient.userAgent = 'DaiZongAI/1.0 (Windows; Proxy)';

    return IOClient(httpClient);
  }

  /// 测试网络连接
  static Future<Map<String, dynamic>> testConnection({
    required String url,
    Duration? timeout,
    bool useProxy = false,
    String? proxyHost,
    int? proxyPort,
  }) async {
    http.Client? client;

    try {
      if (useProxy && proxyHost != null && proxyPort != null) {
        client = createProxyClient(
          proxyHost: proxyHost,
          proxyPort: proxyPort,
          timeout: timeout ?? const Duration(seconds: 30),
        );
      } else {
        client = createSystemClient(
          timeout: timeout ?? const Duration(seconds: 30),
          enableSystemProxy: true,
        );
      }

      final startTime = DateTime.now();

      final response = await client.get(
        Uri.parse(url),
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DaiZongAI/1.0 (Connection Test)',
        },
      ).timeout(timeout ?? const Duration(seconds: 30));

      final endTime = DateTime.now();
      final responseTime = endTime.difference(startTime).inMilliseconds;

      return {
        'success': response.statusCode < 500,
        'statusCode': response.statusCode,
        'responseTime': responseTime,
        'message': '连接测试成功，状态码: ${response.statusCode}，响应时间: ${responseTime}ms',
        'headers': response.headers,
        'useProxy': useProxy,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': '连接测试失败: ${e.toString()}',
        'useProxy': useProxy,
      };
    } finally {
      client?.close();
    }
  }

  /// 检测系统代理配置
  static Future<Map<String, dynamic>> detectSystemProxy() async {
    if (kIsWeb) {
      return {
        'hasProxy': false,
        'message': 'Web平台无法检测系统代理',
      };
    }

    try {
      final results = <String, dynamic>{
        'hasProxy': false,
        'detectedProxies': <String>[],
        'workingProxies': <String>[],
      };

      // 1. 检测环境变量代理
      final httpProxy = Platform.environment['HTTP_PROXY'] ??
          Platform.environment['http_proxy'];
      final httpsProxy = Platform.environment['HTTPS_PROXY'] ??
          Platform.environment['https_proxy'];

      if (httpProxy != null || httpsProxy != null) {
        final proxyUrl = httpsProxy ?? httpProxy;
        results['detectedProxies'].add('环境变量: $proxyUrl');
        results['hasProxy'] = true;
      }

      // 2. 检测常见的本地代理端口
      final commonProxyPorts = [
        '7890', // Clash默认端口
        '1080', // SOCKS代理常用端口
        '8080', // HTTP代理常用端口
        '10809', // V2Ray常用端口
        '7891', // Clash备用端口
        '10808', // V2Ray备用端口
      ];

      for (final port in commonProxyPorts) {
        try {
          // 尝试连接本地代理端口
          final socket = await Socket.connect('127.0.0.1', int.parse(port))
              .timeout(const Duration(seconds: 1));
          socket.destroy();

          final proxyUrl = '127.0.0.1:$port';
          results['detectedProxies'].add('本地代理: $proxyUrl');
          results['workingProxies'].add(proxyUrl);
          results['hasProxy'] = true;
        } catch (e) {
          // 端口不可用，继续检测下一个
        }
      }

      // 3. 生成检测结果消息
      if (results['hasProxy']) {
        final detectedCount = results['detectedProxies'].length;
        final workingCount = results['workingProxies'].length;
        results['message'] = '检测到 $detectedCount 个代理配置，其中 $workingCount 个可用';
      } else {
        results['message'] = '未检测到可用的代理配置';
      }

      return results;
    } catch (e) {
      return {
        'hasProxy': false,
        'error': e.toString(),
        'message': '检测系统代理时出错: ${e.toString()}',
      };
    }
  }

  /// 创建用于Google API的专用客户端，支持智能代理检测
  static http.Client createGoogleApiClient({
    Duration? timeout,
    bool useProxy = false,
    String? proxyHost,
    int? proxyPort,
  }) {
    if (useProxy && proxyHost != null && proxyPort != null) {
      return createProxyClient(
        proxyHost: proxyHost,
        proxyPort: proxyPort,
        timeout: timeout ?? const Duration(seconds: 120),
      );
    } else {
      // 创建支持智能代理检测的客户端
      return _createSmartProxyClient(timeout: timeout ?? const Duration(seconds: 120));
    }
  }

  /// 创建智能代理客户端，能够自动检测和使用可用的代理
  static http.Client _createSmartProxyClient({Duration? timeout}) {
    if (kIsWeb) {
      return http.Client();
    }

    final httpClient = HttpClient();

    // 设置更短的连接超时，避免长时间等待无效代理
    httpClient.connectionTimeout = const Duration(seconds: 5);
    httpClient.idleTimeout = timeout ?? const Duration(seconds: 30);

    // 启用自动重定向
    httpClient.autoUncompress = true;
    httpClient.userAgent = 'DaiZongAI/1.0 (Windows; Smart Proxy)';

    // 设置智能代理检测 - 使用更保守的策略
    httpClient.findProxy = (uri) {
      // 首先检查环境变量
      final httpProxy = Platform.environment['HTTP_PROXY'] ??
          Platform.environment['http_proxy'];
      final httpsProxy = Platform.environment['HTTPS_PROXY'] ??
          Platform.environment['https_proxy'];

      if (httpsProxy != null || httpProxy != null) {
        final proxyUrl = httpsProxy ?? httpProxy;
        print('智能代理：使用环境变量代理: $proxyUrl');
        return 'PROXY $proxyUrl';
      }

      // 对于Google服务，优先尝试直连，如果失败再尝试代理
      if (uri.host.contains('google') || uri.host.contains('googleapis.com')) {
        print('智能代理：检测到Google服务请求');

        // 使用更保守的策略：优先直连，然后尝试常见代理端口
        // 这样可以避免在没有代理的环境中长时间等待
        return 'DIRECT; PROXY 127.0.0.1:7890; PROXY 127.0.0.1:7891';
      }

      // 对于其他服务，默认使用直连
      return 'DIRECT';
    };

    // 完全跳过SSL证书验证
    httpClient.badCertificateCallback = (cert, host, port) {
      print('智能代理客户端 - 跳过SSL证书验证: $host:$port');
      // 对所有主机都返回true，完全跳过证书验证
      return true;
    };

    return IOClient(httpClient);
  }

  /// 检测可用的代理端口
  static Future<String?> detectAvailableProxy() async {
    final commonPorts = ['7890', '7891', '10809', '1080', '8080'];

    for (final port in commonPorts) {
      try {
        final socket = await Socket.connect('127.0.0.1', int.parse(port))
            .timeout(const Duration(seconds: 1));
        socket.destroy();
        print('检测到可用代理端口: $port');
        return '127.0.0.1:$port';
      } catch (e) {
        // 端口不可用，继续检测下一个
        continue;
      }
    }

    print('未检测到可用的代理端口');
    return null;
  }

  /// 创建基于检测结果的Google API客户端
  static Future<http.Client> createDetectedGoogleApiClient({
    Duration? timeout,
  }) async {
    if (kIsWeb) {
      return http.Client();
    }

    // 先检测可用的代理
    final availableProxy = await detectAvailableProxy();

    final httpClient = HttpClient();
    httpClient.connectionTimeout = const Duration(seconds: 15);
    httpClient.idleTimeout = timeout ?? const Duration(seconds: 120);
    httpClient.autoUncompress = true;

    // 使用更标准的User-Agent，模拟浏览器行为
    httpClient.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';

    // 设置代理策略 - 更保守的方法
    if (availableProxy != null) {
      print('检测到可用代理: $availableProxy');
      httpClient.findProxy = (uri) {
        if (uri.host.contains('googleapis.com') || uri.host.contains('generativelanguage.googleapis.com')) {
          // 对于Google API，尝试代理，如果失败则直连
          return 'PROXY $availableProxy; DIRECT';
        }
        return 'DIRECT';
      };
    } else {
      print('未检测到代理，使用直连');
      // 即使没有检测到代理，也尝试常见端口，因为有些代理可能不响应端口检测
      httpClient.findProxy = (uri) {
        if (uri.host.contains('googleapis.com') || uri.host.contains('generativelanguage.googleapis.com')) {
          return 'PROXY 127.0.0.1:7890; PROXY 127.0.0.1:7891; DIRECT';
        }
        return 'DIRECT';
      };
    }

    // 完全跳过SSL证书验证
    httpClient.badCertificateCallback = (cert, host, port) {
      print('检测到的Google API客户端 - 跳过SSL证书验证: $host:$port');
      // 对所有主机都返回true，完全跳过证书验证
      return true;
    };

    return IOClient(httpClient);
  }

  /// 获取代理状态信息
  static Future<Map<String, dynamic>> getProxyStatus() async {
    final result = <String, dynamic>{
      'hasEnvironmentProxy': false,
      'environmentProxy': null,
      'availableProxyPorts': <String>[],
      'recommendedProxy': null,
    };

    // 检查环境变量代理
    final httpProxy = Platform.environment['HTTP_PROXY'] ??
        Platform.environment['http_proxy'];
    final httpsProxy = Platform.environment['HTTPS_PROXY'] ??
        Platform.environment['https_proxy'];

    if (httpProxy != null || httpsProxy != null) {
      result['hasEnvironmentProxy'] = true;
      result['environmentProxy'] = httpsProxy ?? httpProxy;
    }

    // 检查可用的代理端口
    final commonPorts = ['7890', '7891', '10809', '1080', '8080'];
    final availablePorts = <String>[];

    for (final port in commonPorts) {
      try {
        final socket = await Socket.connect('127.0.0.1', int.parse(port))
            .timeout(const Duration(seconds: 1));
        socket.destroy();
        availablePorts.add(port);
      } catch (e) {
        // 端口不可用
      }
    }

    result['availableProxyPorts'] = availablePorts;

    // 推荐代理配置
    if (result['hasEnvironmentProxy']) {
      result['recommendedProxy'] = result['environmentProxy'];
    } else if (availablePorts.isNotEmpty) {
      result['recommendedProxy'] = '127.0.0.1:${availablePorts.first}';
    }

    return result;
  }

  /// 创建简单可靠的Google API客户端
  static http.Client createSimpleGoogleApiClient({
    Duration? timeout,
  }) {
    if (kIsWeb) {
      return http.Client();
    }

    final httpClient = HttpClient();

    // 设置合理的超时时间
    httpClient.connectionTimeout = const Duration(seconds: 15);
    httpClient.idleTimeout = timeout ?? const Duration(seconds: 120);

    // 启用压缩
    httpClient.autoUncompress = true;

    // 使用标准的User-Agent
    httpClient.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';

    // 简单可靠的代理策略
    httpClient.findProxy = (uri) {
      print('正在为 ${uri.host} 查找代理配置...');

      // 首先检查环境变量
      final httpsProxy = Platform.environment['HTTPS_PROXY'] ??
          Platform.environment['https_proxy'];
      final httpProxy = Platform.environment['HTTP_PROXY'] ??
          Platform.environment['http_proxy'];

      if (httpsProxy != null) {
        print('使用HTTPS_PROXY环境变量: $httpsProxy');
        return 'PROXY $httpsProxy';
      } else if (httpProxy != null) {
        print('使用HTTP_PROXY环境变量: $httpProxy');
        return 'PROXY $httpProxy';
      }

      // 对于Google API，尝试常见的代理端口，但使用更保守的策略
      if (uri.host.contains('googleapis.com') || uri.host.contains('generativelanguage.googleapis.com')) {
        print('检测到Google API请求，尝试常见代理端口');
        // 只尝试最常见的端口，并且有快速失败机制
        return 'PROXY 127.0.0.1:7890; DIRECT';
      }

      print('使用直连');
      return 'DIRECT';
    };

    // 设置证书验证
    httpClient.badCertificateCallback = (cert, host, port) {
      return host.contains('googleapis.com') ||
             host.contains('google.com') ||
             host.contains('generativelanguage.googleapis.com');
    };

    return IOClient(httpClient);
  }

  /// 创建强制使用系统代理的Google API客户端
  static http.Client createSystemProxyGoogleApiClient({
    Duration? timeout,
  }) {
    if (kIsWeb) {
      return http.Client();
    }

    final httpClient = HttpClient();

    // 设置超时
    httpClient.connectionTimeout = const Duration(seconds: 30);
    httpClient.idleTimeout = timeout ?? const Duration(seconds: 120);

    // 强制使用系统代理设置
    httpClient.findProxy = (uri) {
      print('正在为 ${uri.host} 查找系统代理...');

      // 检查环境变量
      final httpsProxy = Platform.environment['HTTPS_PROXY'] ??
          Platform.environment['https_proxy'];
      final httpProxy = Platform.environment['HTTP_PROXY'] ??
          Platform.environment['http_proxy'];

      if (httpsProxy != null) {
        print('使用HTTPS_PROXY: $httpsProxy');
        return 'PROXY $httpsProxy';
      } else if (httpProxy != null) {
        print('使用HTTP_PROXY: $httpProxy');
        return 'PROXY $httpProxy';
      }

      // 对于Google API，强制尝试常见代理端口
      if (uri.host.contains('googleapis.com')) {
        print('Google API请求，尝试常见代理端口');
        // 按优先级尝试常见代理端口
        return 'PROXY 127.0.0.1:7890; PROXY 127.0.0.1:7891; PROXY 127.0.0.1:10809; PROXY 127.0.0.1:1080; DIRECT';
      }

      print('使用直连');
      return 'DIRECT';
    };

    // 完全跳过SSL证书验证
    httpClient.badCertificateCallback = (cert, host, port) {
      print('系统代理Google API客户端 - 跳过SSL证书验证: $host:$port');
      // 对所有主机都返回true，完全跳过证书验证
      return true;
    };

    return IOClient(httpClient);
  }

  /// 创建稳定连接客户端，专门用于解决SSL握手问题
  static http.Client createStableConnectionClient({
    Duration? timeout,
  }) {
    if (kIsWeb) {
      return WebProxyClient();
    }

    final httpClient = HttpClient();

    // 设置超时
    httpClient.connectionTimeout = timeout ?? const Duration(seconds: 30);
    httpClient.idleTimeout = timeout ?? const Duration(seconds: 120);

    // 启用自动解压缩
    httpClient.autoUncompress = true;

    // 设置用户代理
    httpClient.userAgent = 'DaiZongAI/1.0 (Stable-Connection-Mode)';

    // 使用系统代理设置
    // 不设置findProxy，让系统自动处理代理

    // 完全跳过SSL证书验证（解决所有SSL握手问题）
    httpClient.badCertificateCallback = (cert, host, port) {
      print('稳定连接客户端 - 跳过SSL证书验证: $host:$port');
      print('证书主题: ${cert.subject}');
      print('证书颁发者: ${cert.issuer}');
      // 对所有主机都返回true，完全跳过证书验证
      return true;
    };

    return IOClient(httpClient);
  }
}

/// Web平台专用的代理客户端
/// 用于解决Web版本的CORS和SSL证书验证问题
class WebProxyClient extends http.BaseClient {
  static String? _proxyBaseUrl;
  final http.Client _inner = http.Client();

  /// 设置代理服务器地址
  static void setProxyUrl(String proxyUrl) {
    _proxyBaseUrl = proxyUrl.endsWith('/') ? proxyUrl.substring(0, proxyUrl.length - 1) : proxyUrl;
    print('Web代理客户端设置代理地址: $_proxyBaseUrl');
  }

  /// 获取当前代理地址
  static String? getProxyUrl() => _proxyBaseUrl;

  /// 自动检测代理地址
  static void autoDetectProxy() {
    // 尝试常见的代理地址
    final commonProxies = [
      'http://localhost:8081',
      'http://127.0.0.1:8081',
      'https://www.dznovel.top',
      'https://dznovel.top',
    ];

    // 默认使用第一个，实际使用时可以通过配置设置
    if (_proxyBaseUrl == null) {
      _proxyBaseUrl = commonProxies.first;
      print('Web代理客户端自动设置代理地址: $_proxyBaseUrl');
    }
  }

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    if (!kIsWeb) {
      // 非Web平台直接转发
      return _inner.send(request);
    }

    // 确保有代理地址
    if (_proxyBaseUrl == null) {
      autoDetectProxy();
    }

    // 检查是否需要代理
    if (_shouldUseProxy(request.url)) {
      final proxyRequest = _createProxyRequest(request);
      print('Web代理请求: ${request.url} -> ${proxyRequest.url}');
      return _inner.send(proxyRequest);
    } else {
      // 不需要代理的请求直接发送
      return _inner.send(request);
    }
  }

  /// 判断是否需要使用代理
  bool _shouldUseProxy(Uri url) {
    final host = url.host.toLowerCase();

    // 需要代理的域名列表
    final proxyDomains = [
      'api.openai-proxy.org',
      'api.openai.com',
      'generativelanguage.googleapis.com',
      'api.anthropic.com',
      'api.deepseek.com',
      'api.siliconflow.cn',
    ];

    return proxyDomains.any((domain) => host.contains(domain));
  }

  /// 创建代理请求
  http.BaseRequest _createProxyRequest(http.BaseRequest originalRequest) {
    if (_proxyBaseUrl == null) {
      throw Exception('代理地址未设置');
    }

    // 构建代理URL
    final originalUrl = originalRequest.url.toString();
    final proxyUrl = '$_proxyBaseUrl/proxy/$originalUrl';

    // 创建新的请求
    final proxyRequest = http.Request(originalRequest.method, Uri.parse(proxyUrl));

    // 复制请求头
    proxyRequest.headers.addAll(originalRequest.headers);

    // 复制请求体
    if (originalRequest is http.Request) {
      proxyRequest.body = originalRequest.body;
    }

    return proxyRequest;
  }

  @override
  void close() {
    _inner.close();
  }
}
